using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Zone Manager
/// Manages all tactical zones and coordinates zone-based AI behavior
/// </summary>
public class ZoneManager : MonoBehaviour
{
    [Header("🎯 Zone Management")]
    public List<ZoneMarker> allZones = new List<ZoneMarker>();
    public bool enableDynamicZoneControl = true;
    public float zoneUpdateInterval = 2f;

    [Header("📊 Zone Statistics")]
    public int teamAControlledZones = 0;
    public int teamBControlledZones = 0;
    public int contestedZones = 0;
    public int neutralZones = 0;

    [Header("🎮 Zone Objectives")]
    public bool enableZoneScoring = true;
    public float zoneControlReward = 0.1f;
    public float zoneContestedPenalty = 0.05f;

    private static ZoneManager instance;
    private Dictionary<TeamID, List<ZoneMarker>> teamControlledZones;

    void Awake()
    {
        if (instance == null)
        {
            instance = this;
            teamControlledZones = new Dictionary<TeamID, List<ZoneMarker>>
            {
                [TeamID.TeamA] = new List<ZoneMarker>(),
                [TeamID.TeamB] = new List<ZoneMarker>()
            };
        }
        else
        {
            Destroy(gameObject);
        }
    }

    void Start()
    {
        // Find all existing zones
        ZoneMarker[] existingZones = FindObjectsOfType<ZoneMarker>();
        foreach (ZoneMarker zone in existingZones)
        {
            RegisterZone(zone);
        }

        if (enableDynamicZoneControl)
        {
            InvokeRepeating(nameof(UpdateZoneControl), zoneUpdateInterval, zoneUpdateInterval);
        }

        Debug.Log($"🎯 Zone Manager initialized with {allZones.Count} zones");
    }

    public void RegisterZone(ZoneMarker zone)
    {
        if (!allZones.Contains(zone))
        {
            allZones.Add(zone);
            Debug.Log($"🎯 Registered zone: {zone.zoneName}");
        }
    }

    public void UnregisterZone(ZoneMarker zone)
    {
        allZones.Remove(zone);

        // Remove from team control lists
        teamControlledZones[TeamID.TeamA].Remove(zone);
        teamControlledZones[TeamID.TeamB].Remove(zone);
    }

    void UpdateZoneControl()
    {
        // Clear previous control data
        teamControlledZones[TeamID.TeamA].Clear();
        teamControlledZones[TeamID.TeamB].Clear();

        teamAControlledZones = 0;
        teamBControlledZones = 0;
        contestedZones = 0;
        neutralZones = 0;

        // Update zone control status
        foreach (ZoneMarker zone in allZones)
        {
            if (zone == null) continue;

            if (zone.isContested)
            {
                contestedZones++;

                // Apply contested penalty to all agents in zone
                if (enableZoneScoring)
                {
                    foreach (VictorAgent agent in zone.currentOccupants)
                    {
                        agent.AddReward(-zoneContestedPenalty);
                    }
                }
            }
            else if (zone.currentOccupants.Count > 0)
            {
                // Zone is controlled by a team
                if (zone.controllingTeam == TeamID.TeamA)
                {
                    teamAControlledZones++;
                    teamControlledZones[TeamID.TeamA].Add(zone);
                }
                else
                {
                    teamBControlledZones++;
                    teamControlledZones[TeamID.TeamB].Add(zone);
                }

                // Apply control reward to controlling team
                if (enableZoneScoring)
                {
                    foreach (VictorAgent agent in zone.currentOccupants)
                    {
                        if (agent.teamID == zone.controllingTeam)
                        {
                            agent.AddReward(zoneControlReward);
                        }
                    }
                }
            }
            else
            {
                neutralZones++;
            }
        }

        // Notify squad managers of zone control changes
        NotifyZoneControlChanges();
    }

    void NotifyZoneControlChanges()
    {
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager != null)
        {
            // Award team bonuses based on zone control
            float teamABonus = teamAControlledZones * 0.02f;
            float teamBBonus = teamBControlledZones * 0.02f;

            // Apply bonuses to all team members
            foreach (VictorAgent agent in squadManager.teamA)
            {
                if (agent != null)
                {
                    agent.AddReward(teamABonus);
                }
            }

            foreach (VictorAgent agent in squadManager.teamB)
            {
                if (agent != null)
                {
                    agent.AddReward(teamBBonus);
                }
            }
        }
    }

    public ZoneMarker GetNearestZone(Vector3 position, ZoneMarker.ZoneType preferredType = ZoneMarker.ZoneType.Patrol)
    {
        ZoneMarker nearestZone = null;
        float nearestDistance = float.MaxValue;

        foreach (ZoneMarker zone in allZones)
        {
            if (zone == null) continue;

            float distance = zone.GetDistanceTo(position);

            // Prefer zones of the specified type
            if (zone.zoneType == preferredType)
            {
                distance *= 0.8f; // 20% preference bonus
            }

            if (distance < nearestDistance)
            {
                nearestDistance = distance;
                nearestZone = zone;
            }
        }

        return nearestZone;
    }

    public ZoneMarker GetBestZoneForAgent(VictorAgent agent, ZoneMarker.ZoneType preferredType = ZoneMarker.ZoneType.Patrol)
    {
        ZoneMarker bestZone = null;
        float bestScore = float.MinValue;

        foreach (ZoneMarker zone in allZones)
        {
            if (zone == null || !zone.CanAcceptAgent(agent)) continue;

            float score = CalculateZoneScore(zone, agent, preferredType);

            if (score > bestScore)
            {
                bestScore = score;
                bestZone = zone;
            }
        }

        return bestZone;
    }

    float CalculateZoneScore(ZoneMarker zone, VictorAgent agent, ZoneMarker.ZoneType preferredType)
    {
        float score = zone.priorityScore;

        // Distance penalty
        float distance = zone.GetDistanceTo(agent.transform.position);
        score -= distance * 0.01f;

        // Type preference bonus
        if (zone.zoneType == preferredType)
        {
            score += 0.5f;
        }

        // Occupancy penalty
        score -= zone.currentOccupants.Count * 0.2f;

        // Team control bonus/penalty
        if (zone.IsControlledBy(agent.teamID))
        {
            score += 0.3f; // Bonus for friendly zones
        }
        else if (zone.currentOccupants.Count > 0 && !zone.isContested)
        {
            score += 0.4f; // Bonus for capturing enemy zones
        }

        // Role-specific scoring
        AgentRoleComponent roleComponent = agent.GetComponent<AgentRoleComponent>();
        if (roleComponent != null)
        {
            score += GetRoleZoneBonus(roleComponent.currentRole, zone.zoneType);
        }

        return score;
    }

    float GetRoleZoneBonus(string role, ZoneMarker.ZoneType zoneType)
    {
        switch (role)
        {
            case "Support":
                if (zoneType == ZoneMarker.ZoneType.Cover || zoneType == ZoneMarker.ZoneType.Fallback)
                    return 0.3f;
                break;
            case "Assault":
                if (zoneType == ZoneMarker.ZoneType.Attack || zoneType == ZoneMarker.ZoneType.Objective)
                    return 0.4f;
                break;
            case "Scout":
                if (zoneType == ZoneMarker.ZoneType.Flank || zoneType == ZoneMarker.ZoneType.Overwatch)
                    return 0.5f;
                break;
            case "Anchor":
                if (zoneType == ZoneMarker.ZoneType.Defense || zoneType == ZoneMarker.ZoneType.Cover)
                    return 0.4f;
                break;
        }

        return 0f;
    }

    public List<ZoneMarker> GetControlledZones(TeamID team)
    {
        return new List<ZoneMarker>(teamControlledZones[team]);
    }

    public List<ZoneMarker> GetContestedZones()
    {
        return allZones.Where(zone => zone != null && zone.isContested).ToList();
    }

    public List<ZoneMarker> GetNeutralZones()
    {
        return allZones.Where(zone => zone != null && zone.currentOccupants.Count == 0).ToList();
    }

    public List<ZoneMarker> GetZonesByType(ZoneMarker.ZoneType zoneType)
    {
        return allZones.Where(zone => zone != null && zone.zoneType == zoneType).ToList();
    }

    public ZoneMarker GetHighestPriorityZone(TeamID team)
    {
        ZoneMarker highestPriorityZone = null;
        float highestPriority = float.MinValue;

        foreach (ZoneMarker zone in allZones)
        {
            if (zone == null) continue;

            float priority = zone.priorityScore;

            // Boost priority for uncontrolled zones
            if (!zone.IsControlledBy(team))
            {
                priority *= 1.5f;
            }

            // Boost priority for contested zones
            if (zone.isContested)
            {
                priority *= 1.3f;
            }

            if (priority > highestPriority)
            {
                highestPriority = priority;
                highestPriorityZone = zone;
            }
        }

        return highestPriorityZone;
    }

    public void CreateZoneConnections()
    {
        Debug.Log("🔗 Creating zone connections...");

        // Connect zones that are within reasonable distance
        float maxConnectionDistance = 25f;

        for (int i = 0; i < allZones.Count; i++)
        {
            for (int j = i + 1; j < allZones.Count; j++)
            {
                ZoneMarker zoneA = allZones[i];
                ZoneMarker zoneB = allZones[j];

                if (zoneA != null && zoneB != null)
                {
                    float distance = zoneA.GetDistanceTo(zoneB.transform.position);
                    if (distance <= maxConnectionDistance)
                    {
                        zoneA.AddConnection(zoneB);
                    }
                }
            }
        }

        Debug.Log("✅ Zone connections created");
    }

    public float GetTeamZoneControlPercentage(TeamID team)
    {
        if (allZones.Count == 0) return 0f;

        int controlledCount = (team == TeamID.TeamA) ? teamAControlledZones : teamBControlledZones;
        return (float)controlledCount / allZones.Count;
    }

    public bool IsTeamWinningZoneControl(TeamID team)
    {
        int teamZones = (team == TeamID.TeamA) ? teamAControlledZones : teamBControlledZones;
        int enemyZones = (team == TeamID.TeamA) ? teamBControlledZones : teamAControlledZones;

        return teamZones > enemyZones;
    }

    void OnGUI()
    {
        if (!Application.isPlaying) return;

        // Display zone control info
        GUI.Box(new Rect(530, 10, 200, 120), "");
        GUI.Label(new Rect(540, 20, 180, 20), "🎯 Zone Control");
        GUI.Label(new Rect(540, 40, 180, 20), $"🔵 Team A: {teamAControlledZones}");
        GUI.Label(new Rect(540, 60, 180, 20), $"🔴 Team B: {teamBControlledZones}");
        GUI.Label(new Rect(540, 80, 180, 20), $"⚔️ Contested: {contestedZones}");
        GUI.Label(new Rect(540, 100, 180, 20), $"⚪ Neutral: {neutralZones}");
    }

    public static ZoneManager Instance => instance;
}
