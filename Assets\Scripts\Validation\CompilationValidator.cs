using UnityEngine;
using System.Collections.Generic;
using System.Reflection;
using System;

/// <summary>
/// Compilation Validator
/// Checks for common compilation issues and missing dependencies
/// </summary>
public class CompilationValidator : MonoBehaviour
{
    [Header("🔍 Validation Results")]
    [TextArea(15, 20)]
    public string validationReport = "Click 'Run Full Validation' to check for compilation issues...";

    [ContextMenu("Run Full Validation")]
    public void RunFullValidation()
    {
        validationReport = "🔍 Running comprehensive compilation validation...\n\n";

        bool allValid = true;

        allValid &= ValidateMLAgentsComponents();
        allValid &= ValidateTeamIDEnum();
        allValid &= ValidateCoreClasses();
        allValid &= ValidateAgentComponents();
        allValid &= ValidateUIComponents();
        allValid &= ValidateTrainingComponents();
        allValid &= ValidateUnityAPIUsage();

        if (allValid)
        {
            validationReport += "\n✅ ALL VALIDATIONS PASSED! No compilation issues detected! 🎉\n";
        }
        else
        {
            validationReport += "\n❌ Some validations failed. Check the issues above.\n";
        }

        Debug.Log(validationReport);
    }

    bool ValidateMLAgentsComponents()
    {
        validationReport += "🤖 VALIDATING ML-AGENTS COMPONENTS:\n";
        bool valid = true;

        try
        {
            // Check if ML-Agents types are available
            Type agentType = Type.GetType("Unity.MLAgents.Agent, Unity.ML-Agents");
            if (agentType != null)
            {
                validationReport += "✅ Unity.MLAgents.Agent found\n";
            }
            else
            {
                validationReport += "❌ Unity.MLAgents.Agent not found - ML-Agents package missing?\n";
                valid = false;
            }

            Type behaviorParamsType = Type.GetType("Unity.MLAgents.Policies.BehaviorParameters, Unity.ML-Agents");
            if (behaviorParamsType != null)
            {
                validationReport += "✅ BehaviorParameters found\n";
            }
            else
            {
                validationReport += "❌ BehaviorParameters not found\n";
                valid = false;
            }
        }
        catch (Exception e)
        {
            validationReport += $"❌ ML-Agents validation error: {e.Message}\n";
            valid = false;
        }

        return valid;
    }

    bool ValidateTeamIDEnum()
    {
        validationReport += "\n👥 VALIDATING TEAM ID ENUM:\n";
        bool valid = true;

        try
        {
            // Check if TeamID enum is properly defined
            Type teamIDType = Type.GetType("TeamID");
            if (teamIDType != null && teamIDType.IsEnum)
            {
                validationReport += "✅ TeamID enum found\n";

                // Check enum values
                string[] enumNames = Enum.GetNames(teamIDType);
                validationReport += $"   Values: {string.Join(", ", enumNames)}\n";

                if (Array.Exists(enumNames, name => name == "TeamA") &&
                    Array.Exists(enumNames, name => name == "TeamB"))
                {
                    validationReport += "✅ Required TeamA and TeamB values found\n";
                }
                else
                {
                    validationReport += "❌ Missing required TeamA or TeamB values\n";
                    valid = false;
                }
            }
            else
            {
                validationReport += "❌ TeamID enum not found or not properly defined\n";
                valid = false;
            }
        }
        catch (Exception e)
        {
            validationReport += $"❌ TeamID validation error: {e.Message}\n";
            valid = false;
        }

        return valid;
    }

    bool ValidateCoreClasses()
    {
        validationReport += "\n🏗️ VALIDATING CORE CLASSES:\n";
        bool valid = true;

        string[] requiredClasses = {
            "SquadMateAgent",
            "VictorAgent",
            "AgentRoleComponent",
            "AIDecisionTreeReader",
            "ZoneMarker",
            "ZoneManager",
            "FlankRoute",
            "FlankRouteManager",
            "CaptureZone",
            "SquadManager",
            "RoleAssigner",
            "WeaponSystem",
            "PUBGInventory",
            "InventorySystem"
        };

        foreach (string className in requiredClasses)
        {
            try
            {
                Type classType = Type.GetType(className);
                if (classType != null)
                {
                    validationReport += $"✅ {className} found\n";
                }
                else
                {
                    validationReport += $"❌ {className} not found\n";
                    valid = false;
                }
            }
            catch (Exception e)
            {
                validationReport += $"❌ {className} validation error: {e.Message}\n";
                valid = false;
            }
        }

        return valid;
    }

    bool ValidateAgentComponents()
    {
        validationReport += "\n🤖 VALIDATING AGENT COMPONENTS:\n";
        bool valid = true;

        // Check for SquadMateAgent instances
        SquadMateAgent[] squadMateAgents = FindObjectsOfType<SquadMateAgent>();
        validationReport += $"✅ Found {squadMateAgents.Length} SquadMateAgent instances\n";

        // Check for VictorAgent instances
        VictorAgent[] victorAgents = FindObjectsOfType<VictorAgent>();
        validationReport += $"✅ Found {victorAgents.Length} VictorAgent instances\n";

        // Check for AgentStats instances
        AgentStats[] agentStats = FindObjectsOfType<AgentStats>();
        validationReport += $"✅ Found {agentStats.Length} AgentStats instances\n";

        return valid;
    }

    bool ValidateUIComponents()
    {
        validationReport += "\n📊 VALIDATING UI COMPONENTS:\n";
        bool valid = true;

        // Check for leaderboard components
        SimpleLeaderboardUI[] simpleUIs = FindObjectsOfType<SimpleLeaderboardUI>();
        LeaderboardUI[] advancedUIs = FindObjectsOfType<LeaderboardUI>();

        validationReport += $"✅ Found {simpleUIs.Length} SimpleLeaderboardUI instances\n";
        validationReport += $"✅ Found {advancedUIs.Length} LeaderboardUI instances\n";

        if (simpleUIs.Length == 0 && advancedUIs.Length == 0)
        {
            validationReport += "⚠️ No leaderboard UI found - consider adding for performance monitoring\n";
        }

        return valid;
    }

    bool ValidateTrainingComponents()
    {
        validationReport += "\n🚀 VALIDATING TRAINING COMPONENTS:\n";
        bool valid = true;

        // Check for training launchers
        SimpleTrainingLauncher[] simpleLaunchers = FindObjectsOfType<SimpleTrainingLauncher>();
        MLAgentsTrainingLauncher[] advancedLaunchers = FindObjectsOfType<MLAgentsTrainingLauncher>();

        validationReport += $"✅ Found {simpleLaunchers.Length} SimpleTrainingLauncher instances\n";
        validationReport += $"✅ Found {advancedLaunchers.Length} MLAgentsTrainingLauncher instances\n";

        // Check for training validators
        TrainingSetupValidator[] validators = FindObjectsOfType<TrainingSetupValidator>();
        validationReport += $"✅ Found {validators.Length} TrainingSetupValidator instances\n";

        return valid;
    }

    bool ValidateUnityAPIUsage()
    {
        validationReport += "\n🔧 VALIDATING UNITY API USAGE:\n";
        bool valid = true;

        try
        {
            // Check Unity version compatibility
            validationReport += $"✅ Unity Version: {Application.unityVersion}\n";

            // Check if NavMesh is available
            Type navMeshType = Type.GetType("UnityEngine.AI.NavMesh, UnityEngine.AIModule");
            if (navMeshType != null)
            {
                validationReport += "✅ NavMesh API available\n";
            }
            else
            {
                validationReport += "❌ NavMesh API not available\n";
                valid = false;
            }

            // Check if UI system is available
            Type canvasType = Type.GetType("UnityEngine.Canvas, UnityEngine.UIModule");
            if (canvasType != null)
            {
                validationReport += "✅ UI System available\n";
            }
            else
            {
                validationReport += "⚠️ UI System not available (UI components may not work)\n";
            }
        }
        catch (Exception e)
        {
            validationReport += $"❌ Unity API validation error: {e.Message}\n";
            valid = false;
        }

        return valid;
    }

    [ContextMenu("Check Component Dependencies")]
    public void CheckComponentDependencies()
    {
        validationReport = "🔍 Checking component dependencies...\n\n";

        // Check SquadMateAgent dependencies
        SquadMateAgent[] agents = FindObjectsOfType<SquadMateAgent>();
        foreach (SquadMateAgent agent in agents)
        {
            if (agent.GetComponent<UnityEngine.AI.NavMeshAgent>() == null)
            {
                validationReport += $"⚠️ {agent.name}: Missing NavMeshAgent component\n";
            }

            if (agent.GetComponent<AgentStats>() == null)
            {
                validationReport += $"⚠️ {agent.name}: Missing AgentStats component\n";
            }
        }

        // Check VictorAgent dependencies
        VictorAgent[] victorAgents = FindObjectsOfType<VictorAgent>();
        foreach (VictorAgent agent in victorAgents)
        {
            if (agent.squadManager == null)
            {
                validationReport += $"⚠️ {agent.name}: Missing SquadManager reference\n";
            }
        }

        validationReport += "\n✅ Component dependency check complete\n";
        Debug.Log(validationReport);
    }
}
