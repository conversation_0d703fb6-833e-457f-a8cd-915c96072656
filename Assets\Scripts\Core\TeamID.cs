using UnityEngine;

/// <summary>
/// Team identification enum for 5v5 team deathmatch
/// </summary>
public enum TeamID
{
    TeamA = 0,
    TeamB = 1,
    Neutral = 2
}

/// <summary>
/// Team utility methods
/// </summary>
public static class TeamIDExtensions
{
    public static TeamID GetOpposingTeam(this TeamID team)
    {
        switch (team)
        {
            case TeamID.TeamA:
                return TeamID.TeamB;
            case TeamID.TeamB:
                return TeamID.TeamA;
            default:
                return TeamID.Neutral;
        }
    }

    public static Color GetTeamColor(this TeamID team)
    {
        switch (team)
        {
            case TeamID.TeamA:
                return Color.blue;
            case TeamID.TeamB:
                return Color.red;
            default:
                return Color.white;
        }
    }

    public static string GetTeamName(this TeamID team)
    {
        switch (team)
        {
            case TeamID.TeamA:
                return "Team Alpha";
            case TeamID.TeamB:
                return "Team Bravo";
            default:
                return "Neutral";
        }
    }
}
