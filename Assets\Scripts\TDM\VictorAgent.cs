using UnityEngine;
using Unity.MLAgents;
using Unity.MLAgents.Sensors;
using Unity.MLAgents.Actuators;
using Unity.MLAgents.Policies;
using System.Collections.Generic;

/// <summary>
/// Victor AI Agent for 5v5 Team Deathmatch
/// Extends SquadMateAgent with team-based tactical behaviors
/// </summary>
public class VictorAgent : SquadMateAgent
{
    [Header("🎮 TDM Configuration")]
    public TeamID teamID;
    public SquadManager squadManager;

    [Header("🎯 TDM Objectives")]
    public float captureZoneRadius = 5f;
    public float teamCoordinationRange = 15f;
    public bool isSquadLeader = false;

    [Header("🔫 TDM Combat")]
    public float engagementRange = 20f;
    public float flankingRange = 10f;
    public int killCount = 0;
    public int deathCount = 0;
    public float damageDealt = 0f;

    [Header("🎯 Performance Metrics")]
    public int reviveCount = 0;
    public int zoneCaptureCount = 0;
    public int flankingSuccessCount = 0;
    public float teamProximityTime = 0f;
    public float survivalTime = 0f;
    public float zoneDefenseTime = 0f;
    public float enemySpottedCount = 0f;
    public float combatAggression = 0.5f;
    public float teamCoordinationWeight = 0.7f;
    public List<string> tacticalPriorities = new List<string>();
    public bool preferDefensivePositions = false;

    [Header("🤝 Team Coordination")]
    public VictorAgent squadLeader;
    public List<VictorAgent> squadMembers = new List<VictorAgent>();
    public Vector3 lastKnownEnemyPosition;
    public float lastEnemySpottedTime;

    // TDM-specific state
    private Transform targetCaptureZone;
    private VictorAgent currentTarget;
    private bool isCapturingZone = false;
    private float respawnTime = 0f;

    public override void Initialize()
    {
        base.Initialize();

        // TDM-specific initialization
        killCount = 0;
        deathCount = 0;
        damageDealt = 0f;

        Debug.Log($"🎮 {name} initialized for TDM (Team: {teamID})");
    }

    public void Initialize(TeamID team, SquadManager manager)
    {
        teamID = team;
        squadManager = manager;

        // Configure behavior parameters for team
        BehaviorParameters behaviorParams = GetComponent<BehaviorParameters>();
        if (behaviorParams != null)
        {
            behaviorParams.TeamId = (int)teamID;
            behaviorParams.BehaviorName = "SquadMateAI"; // Use the same behavior name as in config
        }

        Initialize();
    }

    public override void CollectObservations(VectorSensor sensor)
    {
        // Base SquadMate observations
        base.CollectObservations(sensor);

        // TDM-specific observations (12 additional observations)

        // Team information (3 observations)
        sensor.AddObservation((int)teamID);
        sensor.AddObservation(isSquadLeader ? 1f : 0f);
        sensor.AddObservation(squadMembers.Count / 5f); // Normalize to team size

        // Capture zone information (4 observations)
        if (targetCaptureZone != null)
        {
            Vector3 zoneDirection = (targetCaptureZone.position - transform.position).normalized;
            float zoneDistance = Vector3.Distance(transform.position, targetCaptureZone.position);
            sensor.AddObservation(zoneDirection);
            sensor.AddObservation(zoneDistance / 50f); // Normalize
        }
        else
        {
            sensor.AddObservation(Vector3.zero);
            sensor.AddObservation(0f);
        }

        // Enemy team information (3 observations)
        List<VictorAgent> enemies = squadManager.GetEnemyTeam(teamID);
        VictorAgent nearestEnemy = GetNearestEnemy(enemies);
        if (nearestEnemy != null)
        {
            Vector3 enemyDirection = (nearestEnemy.transform.position - transform.position).normalized;
            float enemyDistance = Vector3.Distance(transform.position, nearestEnemy.transform.position);
            sensor.AddObservation(enemyDirection);
            sensor.AddObservation(enemyDistance / 50f);
        }
        else
        {
            sensor.AddObservation(Vector3.zero);
            sensor.AddObservation(0f);
        }

        // Combat statistics (2 observations)
        sensor.AddObservation(killCount / 10f); // Normalize
        sensor.AddObservation(isCapturingZone ? 1f : 0f);
    }

    public override void OnActionReceived(ActionBuffers actionBuffers)
    {
        // Execute base SquadMate actions
        base.OnActionReceived(actionBuffers);

        // TDM-specific logic
        UpdateTDMBehavior();
        CalculateTDMRewards();
    }

    void UpdateTDMBehavior()
    {
        // Update target capture zone
        if (targetCaptureZone == null)
        {
            targetCaptureZone = squadManager.GetNearestCaptureZone(transform.position);
        }

        // Update squad coordination
        UpdateSquadCoordination();

        // Check for zone capture
        CheckZoneCapture();

        // Update enemy tracking
        UpdateEnemyTracking();
    }

    void UpdateSquadCoordination()
    {
        if (squadManager == null) return;

        List<VictorAgent> friendlies = squadManager.GetFriendlyTeam(teamID);
        squadMembers.Clear();

        // Find nearby squad members
        foreach (VictorAgent friendly in friendlies)
        {
            if (friendly != this && friendly != null)
            {
                float distance = Vector3.Distance(transform.position, friendly.transform.position);
                if (distance <= teamCoordinationRange)
                {
                    squadMembers.Add(friendly);
                }
            }
        }

        // Determine squad leader (agent with most kills or closest to center)
        if (squadMembers.Count > 0 && !isSquadLeader)
        {
            VictorAgent bestLeader = this;
            foreach (VictorAgent member in squadMembers)
            {
                if (member.killCount > bestLeader.killCount)
                {
                    bestLeader = member;
                }
            }

            if (bestLeader == this)
            {
                isSquadLeader = true;
                squadLeader = this;
            }
            else
            {
                squadLeader = bestLeader;
            }
        }
    }

    void CheckZoneCapture()
    {
        if (targetCaptureZone == null) return;

        float distanceToZone = Vector3.Distance(transform.position, targetCaptureZone.position);
        bool wasCapturing = isCapturingZone;
        isCapturingZone = distanceToZone <= captureZoneRadius;

        // Reward for entering capture zone
        if (isCapturingZone && !wasCapturing)
        {
            AddReward(0.1f);
        }

        // Continuous reward for holding zone
        if (isCapturingZone)
        {
            AddReward(0.01f);
        }
    }

    void UpdateEnemyTracking()
    {
        List<VictorAgent> enemies = squadManager.GetEnemyTeam(teamID);
        VictorAgent nearestEnemy = GetNearestEnemy(enemies);

        if (nearestEnemy != null)
        {
            float distance = Vector3.Distance(transform.position, nearestEnemy.transform.position);

            if (distance <= engagementRange)
            {
                lastKnownEnemyPosition = nearestEnemy.transform.position;
                lastEnemySpottedTime = Time.time;
                currentTarget = nearestEnemy;

                // Share enemy position with squad
                ShareEnemyIntel(nearestEnemy);
            }
        }
    }

    void ShareEnemyIntel(VictorAgent enemy)
    {
        foreach (VictorAgent squadMember in squadMembers)
        {
            if (squadMember != null)
            {
                squadMember.ReceiveEnemyIntel(enemy.transform.position);
            }
        }
    }

    public void ReceiveEnemyIntel(Vector3 enemyPosition)
    {
        lastKnownEnemyPosition = enemyPosition;
        lastEnemySpottedTime = Time.time;

        // Small reward for receiving intel
        AddReward(0.02f);
    }

    VictorAgent GetNearestEnemy(List<VictorAgent> enemies)
    {
        VictorAgent nearest = null;
        float minDistance = float.MaxValue;

        foreach (VictorAgent enemy in enemies)
        {
            if (enemy != null && enemy.currentHealth > 0)
            {
                float distance = Vector3.Distance(transform.position, enemy.transform.position);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearest = enemy;
                }
            }
        }

        return nearest;
    }

    void CalculateTDMRewards()
    {
        // Team coordination rewards
        if (squadMembers.Count > 0)
        {
            AddReward(0.005f * squadMembers.Count); // Reward for staying with team
        }

        // Zone control rewards
        if (isCapturingZone)
        {
            AddReward(0.01f); // Continuous zone control reward
        }

        // Combat engagement rewards
        if (currentTarget != null)
        {
            float distance = Vector3.Distance(transform.position, currentTarget.transform.position);
            if (distance <= engagementRange && hasWeapon)
            {
                AddReward(0.005f); // Reward for engaging enemies
            }
        }

        // Survival rewards
        if (currentHealth > maxHealth * 0.8f)
        {
            AddReward(0.002f); // Small reward for staying healthy
        }
    }

    public override void TakeDamage(float damage, bool isHeadshot = false)
    {
        base.TakeDamage(damage, isHeadshot);

        // Check if agent died
        if (currentHealth <= 0)
        {
            OnDeath();
        }
    }

    void OnDeath()
    {
        deathCount++;
        AddReward(-1f); // Large penalty for dying

        // Notify squad manager
        if (squadManager != null)
        {
            squadManager.OnAgentKilled(this, GetLastAttacker());
        }

        Debug.Log($"💀 {name} was eliminated (K/D: {killCount}/{deathCount})");
    }

    VictorAgent GetLastAttacker()
    {
        // Simple implementation - find nearest enemy
        List<VictorAgent> enemies = squadManager.GetEnemyTeam(teamID);
        return GetNearestEnemy(enemies);
    }

    public void OnKillEnemy(VictorAgent enemy)
    {
        killCount++;
        damageDealt += enemy.maxHealth;

        // Large reward for eliminating enemy
        AddReward(1f);

        Debug.Log($"🎯 {name} eliminated {enemy.name} (K/D: {killCount}/{deathCount})");
    }

    public void Respawn(Vector3 position)
    {
        // Reset agent state
        transform.position = position;
        currentHealth = maxHealth;
        isReviving = false;
        isHealing = false;
        isInCombat = false;
        currentTarget = null;

        // Reset physics
        Rigidbody rb = GetComponent<Rigidbody>();
        if (rb != null)
        {
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
        }

        respawnTime = Time.time;

        Debug.Log($"♻️ {name} respawned at {position}");
    }

    public void ResetForNewMatch()
    {
        // Reset match statistics
        killCount = 0;
        deathCount = 0;
        damageDealt = 0f;

        // Reset agent state
        currentHealth = maxHealth;
        isSquadLeader = false;
        squadLeader = null;
        squadMembers.Clear();
        currentTarget = null;
        targetCaptureZone = null;

        Debug.Log($"🔄 {name} reset for new match");
    }

    public override void Heuristic(in ActionBuffers actionsOut)
    {
        base.Heuristic(actionsOut);

        // TDM-specific manual controls
        var discreteActionsOut = actionsOut.DiscreteActions;

        if (Input.GetKey(KeyCode.T)) discreteActionsOut[0] = 11; // Move to capture zone
        if (Input.GetKey(KeyCode.Y)) discreteActionsOut[0] = 12; // Follow squad leader
        if (Input.GetKey(KeyCode.U)) discreteActionsOut[0] = 13; // Flank enemy
    }

    // Zone interaction methods
    public void OnZoneEntered(ZoneMarker zone)
    {
        Debug.Log($"🎯 {name} entered zone: {zone.zoneName}");
    }

    public void OnZoneExited(ZoneMarker zone)
    {
        Debug.Log($"🚪 {name} left zone: {zone.zoneName}");
    }

    public void OnRouteBlocked(FlankRoute route)
    {
        Debug.Log($"🚫 Route blocked for {name}: {route.routeName}");
        // Find alternative route or revert to basic behavior
    }
}
