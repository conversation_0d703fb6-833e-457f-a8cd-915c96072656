%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1001 &8732530667683047946
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: -8712906798048029477, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 827944551381702651, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: m_Name
      value: VictorAgent
      objectReference: {fileID: 0}
    - target: {fileID: 8808443443793940404, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8291480147950799978}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4657519497062480907}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3260382424183296411}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      insertIndex: -1
      addedObject: {fileID: 5882141742621173916}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7904668549435986090}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7038242173372638486}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6065318933241173715}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3562003621481138212}
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6172302060997936237}
  m_SourcePrefab: {fileID: 100100000, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
--- !u!1 &8498652801411241819 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: ddd7231a821fc69418e67b89ac1f498d, type: 3}
  m_PrefabInstance: {fileID: 8732530667683047946}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &8291480147950799978
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8498652801411241819}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5d1c4e0b1822b495aa52bc52839ecb30, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_BrainParameters:
    VectorObservationSize: 1
    NumStackedVectorObservations: 1
    m_ActionSpec:
      m_NumContinuousActions: 0
      BranchSizes: 01000000
    VectorActionSize: 01000000
    VectorActionDescriptions: []
    VectorActionSpaceType: 0
    hasUpgradedBrainParametersWithActionSpec: 1
  m_Model: {fileID: 0}
  m_InferenceDevice: 0
  m_BehaviorType: 0
  m_BehaviorName: VictorTDM
  TeamId: 0
  m_UseChildSensors: 1
  m_UseChildActuators: 1
  m_ObservableAttributeHandling: 0
--- !u!114 &4657519497062480907
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8498652801411241819}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d3cd281de49c2f742b66989158c9cfe1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  agentParameters:
    maxStep: 0
  hasUpgradedFromAgentParameters: 1
  MaxStep: 0
  moveSpeed: 5
  rotationSpeed: 100
  maxHealth: 100
  reviveRange: 2
  followDistance: 3
  useJobSystem: 1
  enableBurstCompilation: 1
  maxObservationDistance: 20
  player: {fileID: 0}
  enemies: []
  medkits: []
  weapons: []
  environment: {fileID: 0}
  mapZones: []
  squadLeader: {fileID: 0}
  currentHealth: 0
  isReviving: 0
  hasWeapon: 0
  currentState: 0
  isInCombat: 0
  isTakingCover: 0
  isHealing: 0
  lastDamageTime: 0
  combatTimeout: 10
  lastKnownEnemyPosition: {x: 0, y: 0, z: 0}
  inventory: {fileID: 0}
  armorSystem: {fileID: 0}
  itemSystem: {fileID: 0}
  teamID: 0
  squadManager: {fileID: 0}
  captureZoneRadius: 5
  teamCoordinationRange: 15
  isSquadLeader: 0
  engagementRange: 20
  flankingRange: 10
  killCount: 0
  deathCount: 0
  damageDealt: 0
  reviveCount: 0
  zoneCaptureCount: 0
  flankingSuccessCount: 0
  teamProximityTime: 0
  survivalTime: 0
  zoneDefenseTime: 0
  enemySpottedCount: 0
  combatAggression: 0.5
  teamCoordinationWeight: 0.7
  tacticalPriorities: []
  preferDefensivePositions: 0
  squadLeader: {fileID: 0}
  squadMembers: []
  lastKnownEnemyPosition: {x: 0, y: 0, z: 0}
  lastEnemySpottedTime: 0
--- !u!114 &3260382424183296411
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8498652801411241819}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b12f797dc00e58545980ed78d241b6e4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  baseCapacity: 150
  currentCapacity: 150
  maxWeight: 200
  items: []
  primaryWeapon:
    itemName: 
    description: 
    itemType: 0
    rarity: 0
    icon: {fileID: 0}
    weight: 1
    stackSize: 1
    isConsumable: 0
    weaponCategory: 0
    damage: 0
    range: 0
    fireRate: 0
    ammoCapacity: 0
    ammoType: 
    healingType: 0
    healAmount: 0
    useTime: 0
    maxHealthPercent: 1
    isBoostItem: 0
    armorLevel: 0
    damageReduction: 0
    isHelmet: 0
    isVest: 0
    capacityBonus: 0
    compatibleWeapons: []
    recoilReduction: 0
    stabilityBonus: 0
    rangeBonus: 0
    throwRange: 30
    fuseTime: 3
    explosionRadius: 5
    explosionDamage: 100
    aiPriority: 1
    isEssential: 0
  secondaryWeapon:
    itemName: 
    description: 
    itemType: 0
    rarity: 0
    icon: {fileID: 0}
    weight: 1
    stackSize: 1
    isConsumable: 0
    weaponCategory: 0
    damage: 0
    range: 0
    fireRate: 0
    ammoCapacity: 0
    ammoType: 
    healingType: 0
    healAmount: 0
    useTime: 0
    maxHealthPercent: 1
    isBoostItem: 0
    armorLevel: 0
    damageReduction: 0
    isHelmet: 0
    isVest: 0
    capacityBonus: 0
    compatibleWeapons: []
    recoilReduction: 0
    stabilityBonus: 0
    rangeBonus: 0
    throwRange: 30
    fuseTime: 3
    explosionRadius: 5
    explosionDamage: 100
    aiPriority: 1
    isEssential: 0
  quickHealSlots:
  - itemName: 
    description: 
    itemType: 0
    rarity: 0
    icon: {fileID: 0}
    weight: 1
    stackSize: 1
    isConsumable: 0
    weaponCategory: 0
    damage: 0
    range: 0
    fireRate: 0
    ammoCapacity: 0
    ammoType: 
    healingType: 0
    healAmount: 0
    useTime: 0
    maxHealthPercent: 1
    isBoostItem: 0
    armorLevel: 0
    damageReduction: 0
    isHelmet: 0
    isVest: 0
    capacityBonus: 0
    compatibleWeapons: []
    recoilReduction: 0
    stabilityBonus: 0
    rangeBonus: 0
    throwRange: 30
    fuseTime: 3
    explosionRadius: 5
    explosionDamage: 100
    aiPriority: 1
    isEssential: 0
  - itemName: 
    description: 
    itemType: 0
    rarity: 0
    icon: {fileID: 0}
    weight: 1
    stackSize: 1
    isConsumable: 0
    weaponCategory: 0
    damage: 0
    range: 0
    fireRate: 0
    ammoCapacity: 0
    ammoType: 
    healingType: 0
    healAmount: 0
    useTime: 0
    maxHealthPercent: 1
    isBoostItem: 0
    armorLevel: 0
    damageReduction: 0
    isHelmet: 0
    isVest: 0
    capacityBonus: 0
    compatibleWeapons: []
    recoilReduction: 0
    stabilityBonus: 0
    rangeBonus: 0
    throwRange: 30
    fuseTime: 3
    explosionRadius: 5
    explosionDamage: 100
    aiPriority: 1
    isEssential: 0
  - itemName: 
    description: 
    itemType: 0
    rarity: 0
    icon: {fileID: 0}
    weight: 1
    stackSize: 1
    isConsumable: 0
    weaponCategory: 0
    damage: 0
    range: 0
    fireRate: 0
    ammoCapacity: 0
    ammoType: 
    healingType: 0
    healAmount: 0
    useTime: 0
    maxHealthPercent: 1
    isBoostItem: 0
    armorLevel: 0
    damageReduction: 0
    isHelmet: 0
    isVest: 0
    capacityBonus: 0
    compatibleWeapons: []
    recoilReduction: 0
    stabilityBonus: 0
    rangeBonus: 0
    throwRange: 30
    fuseTime: 3
    explosionRadius: 5
    explosionDamage: 100
    aiPriority: 1
    isEssential: 0
  - itemName: 
    description: 
    itemType: 0
    rarity: 0
    icon: {fileID: 0}
    weight: 1
    stackSize: 1
    isConsumable: 0
    weaponCategory: 0
    damage: 0
    range: 0
    fireRate: 0
    ammoCapacity: 0
    ammoType: 
    healingType: 0
    healAmount: 0
    useTime: 0
    maxHealthPercent: 1
    isBoostItem: 0
    armorLevel: 0
    damageReduction: 0
    isHelmet: 0
    isVest: 0
    capacityBonus: 0
    compatibleWeapons: []
    recoilReduction: 0
    stabilityBonus: 0
    rangeBonus: 0
    throwRange: 30
    fuseTime: 3
    explosionRadius: 5
    explosionDamage: 100
    aiPriority: 1
    isEssential: 0
  throwableSlots:
  - itemName: 
    description: 
    itemType: 0
    rarity: 0
    icon: {fileID: 0}
    weight: 1
    stackSize: 1
    isConsumable: 0
    weaponCategory: 0
    damage: 0
    range: 0
    fireRate: 0
    ammoCapacity: 0
    ammoType: 
    healingType: 0
    healAmount: 0
    useTime: 0
    maxHealthPercent: 1
    isBoostItem: 0
    armorLevel: 0
    damageReduction: 0
    isHelmet: 0
    isVest: 0
    capacityBonus: 0
    compatibleWeapons: []
    recoilReduction: 0
    stabilityBonus: 0
    rangeBonus: 0
    throwRange: 30
    fuseTime: 3
    explosionRadius: 5
    explosionDamage: 100
    aiPriority: 1
    isEssential: 0
  - itemName: 
    description: 
    itemType: 0
    rarity: 0
    icon: {fileID: 0}
    weight: 1
    stackSize: 1
    isConsumable: 0
    weaponCategory: 0
    damage: 0
    range: 0
    fireRate: 0
    ammoCapacity: 0
    ammoType: 
    healingType: 0
    healAmount: 0
    useTime: 0
    maxHealthPercent: 1
    isBoostItem: 0
    armorLevel: 0
    damageReduction: 0
    isHelmet: 0
    isVest: 0
    capacityBonus: 0
    compatibleWeapons: []
    recoilReduction: 0
    stabilityBonus: 0
    rangeBonus: 0
    throwRange: 30
    fuseTime: 3
    explosionRadius: 5
    explosionDamage: 100
    aiPriority: 1
    isEssential: 0
  currentWeight: 0
  totalItems: 0
  prioritizeHealing: 0
--- !u!114 &5882141742621173916
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8498652801411241819}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  currentHelmet:
    itemName: 
    description: 
    itemType: 0
    rarity: 0
    icon: {fileID: 0}
    weight: 1
    stackSize: 1
    isConsumable: 0
    weaponCategory: 0
    damage: 0
    range: 0
    fireRate: 0
    ammoCapacity: 0
    ammoType: 
    healingType: 0
    healAmount: 0
    useTime: 0
    maxHealthPercent: 1
    isBoostItem: 0
    armorLevel: 0
    damageReduction: 0
    isHelmet: 0
    isVest: 0
    capacityBonus: 0
    compatibleWeapons: []
    recoilReduction: 0
    stabilityBonus: 0
    rangeBonus: 0
    throwRange: 30
    fuseTime: 3
    explosionRadius: 5
    explosionDamage: 100
    aiPriority: 1
    isEssential: 0
  currentVest:
    itemName: 
    description: 
    itemType: 0
    rarity: 0
    icon: {fileID: 0}
    weight: 1
    stackSize: 1
    isConsumable: 0
    weaponCategory: 0
    damage: 0
    range: 0
    fireRate: 0
    ammoCapacity: 0
    ammoType: 
    healingType: 0
    healAmount: 0
    useTime: 0
    maxHealthPercent: 1
    isBoostItem: 0
    armorLevel: 0
    damageReduction: 0
    isHelmet: 0
    isVest: 0
    capacityBonus: 0
    compatibleWeapons: []
    recoilReduction: 0
    stabilityBonus: 0
    rangeBonus: 0
    throwRange: 30
    fuseTime: 3
    explosionRadius: 5
    explosionDamage: 100
    aiPriority: 1
    isEssential: 0
  helmetDurability: 100
  vestDurability: 100
  maxDurability: 100
--- !u!114 &7904668549435986090
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8498652801411241819}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  currentWeaponType: None
  currentDamage: 0
  currentRange: 0
  currentFireRate: 0
  currentAmmo: 0
  totalKills: 0
  totalDamageDealt: 0
  shotsfired: 0
  shotsHit: 0
  preferredWeapons: []
  avoidedWeapons: []
  preferCombatWeapons: 0
--- !u!114 &7038242173372638486
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8498652801411241819}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  maxHealth: 100
  currentHealth: 100
  canRegenerate: 0
  regenRate: 5
  regenDelay: 5
  totalDamageTaken: 0
  totalHealingReceived: 0
  timesHealed: 0
  timesDied: 0
--- !u!114 &6065318933241173715
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8498652801411241819}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0905fe49b46fb8b489ad8f12c648e249, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  basicWeaponReward: 0.1
  arWithScopeReward: 0.3
  healingItemReward: 0.2
  level3GearReward: 0.4
  utilityWhenDownedReward: 0.2
  enemyKillReward: 0.5
  headshotBonusReward: 0.2
  tacticalKillReward: 0.3
  teamworkReward: 0.25
  coverUsageReward: 0.15
  flankingReward: 0.2
  retreatWhenLowReward: 0.1
  healingTimingReward: 0.2
  weaponUpgradeReward: 0.15
  damageTakenPenalty: -0.1
  deathPenalty: -1
  badPositioningPenalty: -0.05
  wastedResourcesPenalty: -0.1
  lootPickupReward: 0.2
  weaponPickupReward: 0.3
  healingReward: 0.2
  combatReward: 0.25
  survivalReward: 0.1
--- !u!54 &3562003621481138212
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8498652801411241819}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 80
  m_CollisionDetection: 0
--- !u!195 &6172302060997936237
NavMeshAgent:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8498652801411241819}
  m_Enabled: 1
  m_AgentTypeID: 0
  m_Radius: 0.5
  m_Speed: 5
  m_Acceleration: 8
  avoidancePriority: 50
  m_AngularSpeed: 120
  m_StoppingDistance: 0
  m_AutoTraverseOffMeshLink: 1
  m_AutoBraking: 1
  m_AutoRepath: 1
  m_Height: 2
  m_BaseOffset: 0
  m_WalkableMask: 4294967295
  m_ObstacleAvoidanceType: 4
