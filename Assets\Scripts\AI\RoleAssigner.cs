using UnityEngine;
using System.Collections.Generic;
using System.Linq;
#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// Dynamic Role Assignment System
/// Assigns and manages agent roles based on decision tree configuration
/// </summary>
public class RoleAssigner : MonoBehaviour
{
    [Header("🎭 Role Assignment")]
    public AIDecisionTreeReader decisionTreeReader;
    public bool enableDynamicRoleSwapping = true;
    public float roleEvaluationInterval = 10f;

    [Header("📊 Team Composition")]
    public int maxSupport = 2;
    public int maxAssault = 2;
    public int maxScout = 1;
    public int maxAnchor = 1;

    [Header("🔄 Role Management")]
    public List<AgentRoleAssignment> teamARoles = new List<AgentRoleAssignment>();
    public List<AgentRoleAssignment> teamBRoles = new List<AgentRoleAssignment>();

    // Role definitions
    private Dictionary<string, RoleDefinition> roleDefinitions;
    private SquadManager squadManager;

    // Available roles
    public enum AgentRoleType
    {
        Support,
        Assault,
        Scout,
        Anchor,
        Flex
    }

    void Start()
    {
        squadManager = GetComponent<SquadManager>();
        if (squadManager == null)
        {
            squadManager = FindObjectOfType<SquadManager>();
        }

        if (decisionTreeReader == null)
        {
            decisionTreeReader = FindObjectOfType<AIDecisionTreeReader>();
        }

        InitializeRoleDefinitions();

        // Wait for decision tree to load
        if (decisionTreeReader != null)
        {
            decisionTreeReader.OnDecisionTreeLoaded += OnDecisionTreeLoaded;
        }

        // Start role evaluation
        if (enableDynamicRoleSwapping)
        {
            InvokeRepeating(nameof(EvaluateRoleAssignments), roleEvaluationInterval, roleEvaluationInterval);
        }
    }

    void OnDecisionTreeLoaded(AIDecisionTree decisionTree)
    {
        Debug.Log("🎭 Decision tree loaded, assigning roles...");
        AssignInitialRoles();
    }

    void InitializeRoleDefinitions()
    {
        roleDefinitions = new Dictionary<string, RoleDefinition>
        {
            ["Support"] = new RoleDefinition
            {
                roleName = "Support",
                priorities = new List<string> { "Revive", "StayBehind", "PingEnemy", "SupplyDrop" },
                preferredWeapons = new List<string> { "M416", "UMP45", "FirstAidKit", "SmokeGrenade" },
                avoidWeapons = new List<string> { "SniperRifle", "Shotgun" },
                behaviorModifiers = new Dictionary<string, float>
                {
                    ["aggression"] = 0.3f,
                    ["teamwork"] = 0.9f,
                    ["survival"] = 0.8f,
                    ["exploration"] = 0.4f
                }
            },
            ["Assault"] = new RoleDefinition
            {
                roleName = "Assault",
                priorities = new List<string> { "PushEnemy", "LeadEntry", "ZoneCapture", "Engage" },
                preferredWeapons = new List<string> { "M416", "AKM", "Grenade", "FlashBang" },
                avoidWeapons = new List<string> { "SniperRifle" },
                behaviorModifiers = new Dictionary<string, float>
                {
                    ["aggression"] = 0.8f,
                    ["teamwork"] = 0.6f,
                    ["survival"] = 0.5f,
                    ["exploration"] = 0.7f
                }
            },
            ["Scout"] = new RoleDefinition
            {
                roleName = "Scout",
                priorities = new List<string> { "Flank", "Spot", "QuickRotation", "Intel" },
                preferredWeapons = new List<string> { "UMP45", "Vector", "Smoke", "Grenade" },
                avoidWeapons = new List<string> { "M249", "Shotgun" },
                behaviorModifiers = new Dictionary<string, float>
                {
                    ["aggression"] = 0.6f,
                    ["teamwork"] = 0.5f,
                    ["survival"] = 0.7f,
                    ["exploration"] = 0.9f
                }
            },
            ["Anchor"] = new RoleDefinition
            {
                roleName = "Anchor",
                priorities = new List<string> { "HoldZone", "SuppressEnemy", "DefendPosition" },
                preferredWeapons = new List<string> { "M249", "Scar-L", "4xScope", "Grenade" },
                avoidWeapons = new List<string> { "Shotgun", "Vector" },
                behaviorModifiers = new Dictionary<string, float>
                {
                    ["aggression"] = 0.4f,
                    ["teamwork"] = 0.7f,
                    ["survival"] = 0.9f,
                    ["exploration"] = 0.2f
                }
            }
        };

        Debug.Log($"✅ Initialized {roleDefinitions.Count} role definitions");
    }

    public void AssignInitialRoles()
    {
        Debug.Log("🎭 Assigning initial roles to teams...");

        // Assign roles to Team A
        if (squadManager.teamA.Count > 0)
        {
            AssignRolesToTeam(squadManager.teamA, teamARoles, TeamID.TeamA);
        }

        // Assign roles to Team B
        if (squadManager.teamB.Count > 0)
        {
            AssignRolesToTeam(squadManager.teamB, teamBRoles, TeamID.TeamB);
        }

        Debug.Log("✅ Initial role assignment complete");
    }

    void AssignRolesToTeam(List<VictorAgent> team, List<AgentRoleAssignment> roleList, TeamID teamID)
    {
        roleList.Clear();

        // Define optimal team composition
        List<AgentRoleType> optimalComposition = new List<AgentRoleType>
        {
            AgentRoleType.Support,
            AgentRoleType.Support,
            AgentRoleType.Assault,
            AgentRoleType.Scout,
            AgentRoleType.Anchor
        };

        // Assign roles based on optimal composition
        for (int i = 0; i < team.Count && i < optimalComposition.Count; i++)
        {
            AgentRoleType roleType = optimalComposition[i];
            VictorAgent agent = team[i];

            AgentRoleAssignment assignment = new AgentRoleAssignment
            {
                agent = agent,
                roleType = roleType,
                roleName = roleType.ToString(),
                assignedTime = Time.time,
                performance = 1f
            };

            roleList.Add(assignment);
            ApplyRoleToAgent(agent, roleType);

            Debug.Log($"🎭 {agent.name} assigned role: {roleType} (Team {teamID})");
        }
    }

    public void ApplyRoleToAgent(VictorAgent agent, AgentRoleType roleType)
    {
        string roleName = roleType.ToString();

        if (!roleDefinitions.ContainsKey(roleName))
        {
            Debug.LogWarning($"⚠️ Role definition not found: {roleName}");
            return;
        }

        RoleDefinition roleDef = roleDefinitions[roleName];

        // Apply role to agent
        AgentRoleComponent roleComponent = agent.GetComponent<AgentRoleComponent>();
        if (roleComponent == null)
        {
            roleComponent = agent.gameObject.AddComponent<AgentRoleComponent>();
        }

        roleComponent.ApplyRole(roleDef);

        // Update agent behavior based on role
        UpdateAgentBehavior(agent, roleDef);

        Debug.Log($"✅ Applied {roleName} role to {agent.name}");
    }

    void UpdateAgentBehavior(VictorAgent agent, RoleDefinition roleDef)
    {
        // Update movement and combat preferences
        if (roleDef.behaviorModifiers.ContainsKey("aggression"))
        {
            float aggression = roleDef.behaviorModifiers["aggression"];
            agent.combatAggression = aggression;
        }

        if (roleDef.behaviorModifiers.ContainsKey("teamwork"))
        {
            float teamwork = roleDef.behaviorModifiers["teamwork"];
            agent.teamCoordinationWeight = teamwork;
        }

        // Update weapon preferences
        WeaponSystem weaponSystem = agent.GetComponent<WeaponSystem>();
        if (weaponSystem != null)
        {
            weaponSystem.preferredWeapons = roleDef.preferredWeapons;
            weaponSystem.avoidedWeapons = roleDef.avoidWeapons;
        }

        // Update tactical priorities
        agent.tacticalPriorities = roleDef.priorities;
    }

    void EvaluateRoleAssignments()
    {
        if (!enableDynamicRoleSwapping) return;

        Debug.Log("🔄 Evaluating role assignments...");

        EvaluateTeamRoles(teamARoles, TeamID.TeamA);
        EvaluateTeamRoles(teamBRoles, TeamID.TeamB);
    }

    void EvaluateTeamRoles(List<AgentRoleAssignment> roleList, TeamID teamID)
    {
        foreach (AgentRoleAssignment assignment in roleList)
        {
            if (assignment.agent == null) continue;

            // Calculate performance based on role-specific metrics
            float performance = CalculateRolePerformance(assignment);
            assignment.performance = performance;

            // Consider role swap if performance is poor
            if (performance < 0.3f && Time.time - assignment.assignedTime > 30f)
            {
                ConsiderRoleSwap(assignment, roleList);
            }
        }
    }

    float CalculateRolePerformance(AgentRoleAssignment assignment)
    {
        VictorAgent agent = assignment.agent;
        AgentRoleType roleType = assignment.roleType;

        float performance = 0.5f; // Base performance

        switch (roleType)
        {
            case AgentRoleType.Support:
                // Support performance based on revives and team proximity
                performance += agent.reviveCount * 0.2f;
                performance += agent.teamProximityTime * 0.1f;
                break;

            case AgentRoleType.Assault:
                // Assault performance based on kills and zone captures
                performance += agent.killCount * 0.3f;
                performance += agent.zoneCaptureCount * 0.2f;
                break;

            case AgentRoleType.Scout:
                // Scout performance based on flanking and intel
                performance += agent.flankingSuccessCount * 0.4f;
                performance += agent.enemySpottedCount * 0.1f;
                break;

            case AgentRoleType.Anchor:
                // Anchor performance based on zone defense and survival
                performance += agent.zoneDefenseTime * 0.2f;
                performance += agent.survivalTime * 0.1f;
                break;
        }

        return Mathf.Clamp01(performance);
    }

    void ConsiderRoleSwap(AgentRoleAssignment poorPerformer, List<AgentRoleAssignment> teamRoles)
    {
        // Find a better role for this agent
        AgentRoleType bestRole = FindBestRoleForAgent(poorPerformer.agent);

        if (bestRole != poorPerformer.roleType)
        {
            Debug.Log($"🔄 Swapping {poorPerformer.agent.name} from {poorPerformer.roleType} to {bestRole}");

            poorPerformer.roleType = bestRole;
            poorPerformer.roleName = bestRole.ToString();
            poorPerformer.assignedTime = Time.time;
            poorPerformer.performance = 1f;

            ApplyRoleToAgent(poorPerformer.agent, bestRole);
        }
    }

    AgentRoleType FindBestRoleForAgent(VictorAgent agent)
    {
        // Simple heuristic based on agent stats
        if (agent.reviveCount > agent.killCount)
            return AgentRoleType.Support;
        else if (agent.killCount > 3)
            return AgentRoleType.Assault;
        else if (agent.flankingSuccessCount > 1)
            return AgentRoleType.Scout;
        else
            return AgentRoleType.Anchor;
    }

    public AgentRoleAssignment GetAgentRole(VictorAgent agent)
    {
        // Search in both teams
        foreach (var assignment in teamARoles)
        {
            if (assignment.agent == agent)
                return assignment;
        }

        foreach (var assignment in teamBRoles)
        {
            if (assignment.agent == agent)
                return assignment;
        }

        return null;
    }

    public List<VictorAgent> GetAgentsByRole(AgentRoleType roleType, TeamID teamID)
    {
        List<AgentRoleAssignment> roleList = (teamID == TeamID.TeamA) ? teamARoles : teamBRoles;

        return roleList
            .Where(assignment => assignment.roleType == roleType && assignment.agent != null)
            .Select(assignment => assignment.agent)
            .ToList();
    }

    void OnGUI()
    {
        if (!Application.isPlaying) return;

        // Display role assignments
        GUI.Box(new Rect(220, 10, 300, 200), "");
#if UNITY_EDITOR
        GUI.Label(new Rect(230, 20, 280, 20), "🎭 Role Assignments", EditorGUIUtility.GetBuiltinSkin(EditorSkin.Inspector).label);
#else
        GUI.Label(new Rect(230, 20, 280, 20), "🎭 Role Assignments");
#endif

        int yOffset = 40;

        // Team A roles
        GUI.Label(new Rect(230, yOffset, 280, 20), "🔵 Team A:");
        yOffset += 20;
        foreach (var assignment in teamARoles)
        {
            if (assignment.agent != null)
            {
                string roleText = $"{assignment.agent.name}: {assignment.roleType} ({assignment.performance:F1})";
                GUI.Label(new Rect(240, yOffset, 260, 15), roleText);
                yOffset += 15;
            }
        }

        yOffset += 10;

        // Team B roles
        GUI.Label(new Rect(230, yOffset, 280, 20), "🔴 Team B:");
        yOffset += 20;
        foreach (var assignment in teamBRoles)
        {
            if (assignment.agent != null)
            {
                string roleText = $"{assignment.agent.name}: {assignment.roleType} ({assignment.performance:F1})";
                GUI.Label(new Rect(240, yOffset, 260, 15), roleText);
                yOffset += 15;
            }
        }
    }
}

[System.Serializable]
public class AgentRoleAssignment
{
    public VictorAgent agent;
    public RoleAssigner.AgentRoleType roleType;
    public string roleName;
    public float assignedTime;
    public float performance;
}

[System.Serializable]
public class RoleDefinition
{
    public string roleName;
    public List<string> priorities;
    public List<string> preferredWeapons;
    public List<string> avoidWeapons;
    public Dictionary<string, float> behaviorModifiers;
}
