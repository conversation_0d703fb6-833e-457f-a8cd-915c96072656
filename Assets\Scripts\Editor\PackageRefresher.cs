using UnityEngine;
using UnityEditor;
using UnityEditor.PackageManager;
using UnityEditor.PackageManager.Requests;

/// <summary>
/// Editor utility to refresh packages and ensure AI Navigation is properly loaded
/// </summary>
public class PackageRefresher : EditorWindow
{
    private static ListRequest listRequest;
    private static AddRequest addRequest;

    [MenuItem("SquadMate AI/🔄 Refresh Packages")]
    public static void ShowWindow()
    {
        GetWindow<PackageRefresher>("Package Refresher");
    }

    [MenuItem("SquadMate AI/🗺️ Install AI Navigation")]
    public static void InstallAINavigation()
    {
        Debug.Log("🔄 Installing AI Navigation package...");
        addRequest = Client.Add("com.unity.ai.navigation");
        EditorApplication.update += AddProgress;
    }

    void OnGUI()
    {
        GUILayout.Label("🔄 Package Management", EditorStyles.boldLabel);
        GUILayout.Space(10);

        if (GUILayout.But<PERSON>("🗺️ Install AI Navigation Package"))
        {
            InstallAINavigation();
        }

        if (GUILayout.Button("📋 List Installed Packages"))
        {
            ListPackages();
        }

        if (GUILayout.Button("🔄 Refresh Asset Database"))
        {
            AssetDatabase.Refresh();
            Debug.Log("✅ Asset database refreshed");
        }

        GUILayout.Space(10);
        GUILayout.Label("After installing AI Navigation:", EditorStyles.boldLabel);
        GUILayout.Label("• Window > AI > Navigation should appear");
        GUILayout.Label("• NavMeshSurface component will be available");
        GUILayout.Label("• Automatic NavMesh baking will work");
    }

    static void ListPackages()
    {
        listRequest = Client.List();
        EditorApplication.update += ListProgress;
    }

    static void ListProgress()
    {
        if (listRequest.IsCompleted)
        {
            if (listRequest.Status == StatusCode.Success)
            {
                Debug.Log("📋 Installed packages:");
                foreach (var package in listRequest.Result)
                {
                    if (package.name.Contains("ai") || package.name.Contains("ml-agents"))
                    {
                        Debug.Log($"  ✅ {package.name} v{package.version}");
                    }
                }
            }
            else if (listRequest.Status >= StatusCode.Failure)
            {
                Debug.LogError($"❌ Failed to list packages: {listRequest.Error.message}");
            }

            EditorApplication.update -= ListProgress;
        }
    }

    static void AddProgress()
    {
        if (addRequest.IsCompleted)
        {
            if (addRequest.Status == StatusCode.Success)
            {
                Debug.Log($"✅ Successfully installed: {addRequest.Result.displayName}");
                Debug.Log("🗺️ AI Navigation package installed! Window > AI > Navigation should now be available.");
                AssetDatabase.Refresh();
            }
            else if (addRequest.Status >= StatusCode.Failure)
            {
                Debug.LogError($"❌ Failed to install AI Navigation: {addRequest.Error.message}");
            }

            EditorApplication.update -= AddProgress;
        }
    }
}
