behaviors:
  VictorTDM:
    trainer_type: poca
    hyperparameters:
      batch_size: 2048
      buffer_size: 20480
      learning_rate: 3e-4
      beta: 5e-3
      epsilon: 0.2
      lambd: 0.95
      num_epoch: 3
      learning_rate_schedule: linear

    network_settings:
      normalize: true
      hidden_units: 512
      num_layers: 3
      vis_encode_type: simple
      memory:
        sequence_length: 64
        memory_size: 256

    reward_signals:
      extrinsic:
        gamma: 0.99
        strength: 1.0
      curiosity:
        gamma: 0.99
        strength: 0.02
        encoding_size: 256
        learning_rate: 1e-3

    max_steps: 5000000
    time_horizon: 1000
    summary_freq: 10000
    threaded: true

    self_play:
      save_steps: 50000
      team_change: 200000
      swap_steps: 10000
      play_against_latest_model_ratio: 0.5
      window: 10

env_settings:
  env_path: null
  env_args: null
  base_port: 5005
  num_envs: 1
  seed: -1

engine_settings:
  width: 1280
  height: 720
  quality_level: 1
  time_scale: 20
  target_frame_rate: 60
  capture_frame_rate: 60
  no_graphics: false

checkpoint_settings:
  run_id: victor_tdm_training
  initialize_from: null
  load_model: false
  resume: false
  force: false
  train_model: true
  inference: false
  results_dir: results