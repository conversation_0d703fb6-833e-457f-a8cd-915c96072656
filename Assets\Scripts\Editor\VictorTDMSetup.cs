using UnityEngine;
using UnityEditor;
using System.IO;
using System.Collections.Generic;

/// <summary>
/// Unity Editor setup script for Victor 5v5 TDM system
/// Automates the integration of Victor character models and TDM arena
/// </summary>
public class VictorTDMSetup : EditorWindow
{
    [Header("🎮 Victor TDM Setup")]
    public GameObject victorModelPrefab;
    public GameObject tdmArenaPrefab;
    public Material[] victorMaterials = new Material[3];

    private Vector2 scrollPosition;
    private bool showAdvancedOptions = false;

    [MenuItem("SquadMate AI/🎮 Victor TDM Setup")]
    public static void ShowWindow()
    {
        GetWindow<VictorTDMSetup>("Victor TDM Setup");
    }

    void OnGUI()
    {
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        GUILayout.Label("🎮 Victor 5v5 TDM Setup", EditorStyles.boldLabel);
        GUILayout.Space(10);

        // Asset Assignment Section
        GUILayout.Label("📁 Asset Assignment", EditorStyles.boldLabel);

        victorModelPrefab = (GameObject)EditorGUILayout.ObjectField(
            "Victor Model Prefab", victorModelPrefab, typeof(GameObject), false);

        tdmArenaPrefab = (GameObject)EditorGUILayout.ObjectField(
            "TDM Arena Prefab", tdmArenaPrefab, typeof(GameObject), false);

        GUILayout.Label("🎨 Victor Materials", EditorStyles.boldLabel);
        victorMaterials[0] = (Material)EditorGUILayout.ObjectField(
            "Face Material", victorMaterials[0], typeof(Material), false);
        victorMaterials[1] = (Material)EditorGUILayout.ObjectField(
            "Body Material", victorMaterials[1], typeof(Material), false);
        victorMaterials[2] = (Material)EditorGUILayout.ObjectField(
            "Gear Material", victorMaterials[2], typeof(Material), false);

        GUILayout.Space(10);

        // Quick Setup Section
        GUILayout.Label("🚀 Quick Setup", EditorStyles.boldLabel);

        if (GUILayout.Button("🔍 Auto-Find Assets"))
        {
            AutoFindAssets();
        }

        if (GUILayout.Button("🎮 Create Complete TDM Scene"))
        {
            CreateCompleteTDMScene();
        }

        if (GUILayout.Button("🧍‍♂️ Setup Victor Prefab"))
        {
            SetupVictorPrefab();
        }

        if (GUILayout.Button("🗺️ Setup TDM Arena"))
        {
            SetupTDMArena();
        }

        GUILayout.Space(10);

        // Individual Setup Section
        GUILayout.Label("🔧 Individual Setup", EditorStyles.boldLabel);

        if (GUILayout.Button("Create Victor Materials"))
        {
            CreateVictorMaterials();
        }

        if (GUILayout.Button("Setup Spawn Points"))
        {
            SetupSpawnPoints();
        }

        if (GUILayout.Button("Create Capture Zones"))
        {
            CreateCaptureZones();
        }

        if (GUILayout.Button("Setup Weapon Spawns"))
        {
            SetupWeaponSpawns();
        }

        GUILayout.Space(10);

        // Advanced Options
        showAdvancedOptions = EditorGUILayout.Foldout(showAdvancedOptions, "🔬 Advanced Options");
        if (showAdvancedOptions)
        {
            if (GUILayout.Button("Generate Training Config"))
            {
                GenerateTDMTrainingConfig();
            }

            if (GUILayout.Button("Create Team Materials"))
            {
                CreateTeamMaterials();
            }

            if (GUILayout.Button("Setup NavMesh"))
            {
                SetupNavMesh();
            }

            if (GUILayout.Button("Validate TDM Setup"))
            {
                ValidateTDMSetup();
            }
        }

        EditorGUILayout.EndScrollView();
    }

    void AutoFindAssets()
    {
        Debug.Log("🔍 Auto-finding Victor TDM assets...");

        // Find Victor model
        string[] victorGuids = AssetDatabase.FindAssets("Victor t:GameObject");
        foreach (string guid in victorGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (path.Contains("Victor") && path.EndsWith(".fbx"))
            {
                victorModelPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                Debug.Log($"✅ Found Victor model: {path}");
                break;
            }
        }

        // Find TDM arena
        string[] tdmGuids = AssetDatabase.FindAssets("Tdm t:GameObject");
        foreach (string guid in tdmGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            if (path.Contains("Tdm") && path.EndsWith(".fbx"))
            {
                tdmArenaPrefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                Debug.Log($"✅ Found TDM arena: {path}");
                break;
            }
        }

        // Find Victor textures
        string[] textureGuids = AssetDatabase.FindAssets("mat0 t:Texture2D");
        foreach (string guid in textureGuids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            Debug.Log($"📄 Found texture: {path}");
        }

        if (victorModelPrefab != null && tdmArenaPrefab != null)
        {
            EditorUtility.DisplayDialog("Success", "Assets found successfully!", "OK");
        }
        else
        {
            EditorUtility.DisplayDialog("Warning", "Some assets not found. Please assign manually.", "OK");
        }
    }

    void CreateCompleteTDMScene()
    {
        Debug.Log("🎮 Creating complete TDM scene...");

        // Create new scene
        var newScene = UnityEditor.SceneManagement.EditorSceneManager.NewScene(
            UnityEditor.SceneManagement.NewSceneSetup.EmptyScene,
            UnityEditor.SceneManagement.NewSceneMode.Single);

        // Setup basic lighting
        GameObject light = new GameObject("Directional Light");
        Light lightComponent = light.AddComponent<Light>();
        lightComponent.type = LightType.Directional;
        light.transform.rotation = Quaternion.Euler(50f, -30f, 0f);

        // Create main camera
        GameObject camera = new GameObject("Main Camera");
        camera.AddComponent<Camera>();
        camera.AddComponent<AudioListener>();
        camera.transform.position = new Vector3(0, 10, -20);
        camera.transform.rotation = Quaternion.Euler(15f, 0f, 0f);

        // Setup TDM arena
        SetupTDMArena();

        // Setup Victor prefab
        SetupVictorPrefab();

        // Create squad manager
        GameObject squadManagerObj = new GameObject("SquadManager");
        SquadManager squadManager = squadManagerObj.AddComponent<SquadManager>();

        // Assign Victor prefab to squad manager
        if (victorModelPrefab != null)
        {
            squadManager.victorAgentPrefab = victorModelPrefab;
        }

        // Setup spawn points
        SetupSpawnPoints();

        // Create capture zones
        CreateCaptureZones();

        // Setup weapon spawns
        SetupWeaponSpawns();

        Debug.Log("✅ Complete TDM scene created!");
        EditorUtility.DisplayDialog("Success", "Complete TDM scene created successfully!", "OK");
    }

    void SetupVictorPrefab()
    {
        if (victorModelPrefab == null)
        {
            EditorUtility.DisplayDialog("Error", "Victor model prefab not assigned!", "OK");
            return;
        }

        Debug.Log("🧍‍♂️ Setting up Victor prefab...");

        // Create prefab instance
        GameObject victorInstance = PrefabUtility.InstantiatePrefab(victorModelPrefab) as GameObject;
        victorInstance.name = "VictorAgent_Prefab";

        // Add required components
        if (victorInstance.GetComponent<VictorAgent>() == null)
            victorInstance.AddComponent<VictorAgent>();

        if (victorInstance.GetComponent<PUBGInventory>() == null)
            victorInstance.AddComponent<PUBGInventory>();

        if (victorInstance.GetComponent<PUBGArmorSystem>() == null)
            victorInstance.AddComponent<PUBGArmorSystem>();

        if (victorInstance.GetComponent<WeaponSystem>() == null)
            victorInstance.AddComponent<WeaponSystem>();

        if (victorInstance.GetComponent<HealthSystem>() == null)
            victorInstance.AddComponent<HealthSystem>();

        if (victorInstance.GetComponent<DynamicRewardSystem>() == null)
            victorInstance.AddComponent<DynamicRewardSystem>();

        // Setup Rigidbody
        Rigidbody rb = victorInstance.GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = victorInstance.AddComponent<Rigidbody>();
        }
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;

        // Setup NavMeshAgent
        UnityEngine.AI.NavMeshAgent navAgent = victorInstance.GetComponent<UnityEngine.AI.NavMeshAgent>();
        if (navAgent == null)
        {
            navAgent = victorInstance.AddComponent<UnityEngine.AI.NavMeshAgent>();
        }
        navAgent.speed = 5f;
        navAgent.acceleration = 8f;
        navAgent.angularSpeed = 120f;

        // Setup BehaviorParameters for ML-Agents
        Unity.MLAgents.Policies.BehaviorParameters behaviorParams = victorInstance.GetComponent<Unity.MLAgents.Policies.BehaviorParameters>();
        if (behaviorParams == null)
        {
            behaviorParams = victorInstance.AddComponent<Unity.MLAgents.Policies.BehaviorParameters>();
        }
        behaviorParams.BehaviorName = "VictorTDM";
        behaviorParams.BehaviorType = Unity.MLAgents.Policies.BehaviorType.Default;

        // Apply materials if available
        ApplyVictorMaterials(victorInstance);

        // Save as prefab
        string prefabPath = "Assets/Prefabs/VictorAgent.prefab";
        Directory.CreateDirectory("Assets/Prefabs");
        PrefabUtility.SaveAsPrefabAsset(victorInstance, prefabPath);

        // Clean up instance
        DestroyImmediate(victorInstance);

        Debug.Log($"✅ Victor prefab created at {prefabPath}");
    }

    void ApplyVictorMaterials(GameObject victor)
    {
        if (victorMaterials[0] == null || victorMaterials[1] == null || victorMaterials[2] == null)
        {
            CreateVictorMaterials();
        }

        Renderer[] renderers = victor.GetComponentsInChildren<Renderer>();
        foreach (Renderer renderer in renderers)
        {
            Material[] materials = renderer.materials;

            for (int i = 0; i < materials.Length; i++)
            {
                string materialName = materials[i].name.ToLower();

                if (materialName.Contains("mat0") && !materialName.Contains("."))
                {
                    materials[i] = victorMaterials[0]; // Face
                }
                else if (materialName.Contains("mat0.001"))
                {
                    materials[i] = victorMaterials[1]; // Body
                }
                else if (materialName.Contains("mat0.002"))
                {
                    materials[i] = victorMaterials[2]; // Gear
                }
            }

            renderer.materials = materials;
        }
    }

    void CreateVictorMaterials()
    {
        Debug.Log("🎨 Creating Victor materials...");

        Directory.CreateDirectory("Assets/Materials/Victor");

        // Find textures
        Texture2D faceTexture = FindTexture("mat0_baseColor");
        Texture2D bodyTexture = FindTexture("mat0.001_baseColor");
        Texture2D gearTexture = FindTexture("mat0.002_baseColor");

        // Create materials
        victorMaterials[0] = CreateMaterial("Victor_Face", faceTexture);
        victorMaterials[1] = CreateMaterial("Victor_Body", bodyTexture);
        victorMaterials[2] = CreateMaterial("Victor_Gear", gearTexture);

        Debug.Log("✅ Victor materials created");
    }

    Texture2D FindTexture(string name)
    {
        string[] guids = AssetDatabase.FindAssets(name + " t:Texture2D");
        if (guids.Length > 0)
        {
            string path = AssetDatabase.GUIDToAssetPath(guids[0]);
            return AssetDatabase.LoadAssetAtPath<Texture2D>(path);
        }
        return null;
    }

    Material CreateMaterial(string name, Texture2D texture)
    {
        Material material = new Material(Shader.Find("Standard"));
        material.name = name;

        if (texture != null)
        {
            material.mainTexture = texture;
        }

        string path = $"Assets/Materials/Victor/{name}.mat";
        AssetDatabase.CreateAsset(material, path);

        return material;
    }

    void SetupTDMArena()
    {
        if (tdmArenaPrefab == null)
        {
            EditorUtility.DisplayDialog("Error", "TDM arena prefab not assigned!", "OK");
            return;
        }

        Debug.Log("🗺️ Setting up TDM arena...");

        // Instantiate arena
        GameObject arena = PrefabUtility.InstantiatePrefab(tdmArenaPrefab) as GameObject;
        arena.name = "TDM_Arena";

        // Add TDM Environment component
        if (arena.GetComponent<TDMEnvironment>() == null)
        {
            arena.AddComponent<TDMEnvironment>();
        }

        // Mark static for NavMesh
        MeshRenderer[] renderers = arena.GetComponentsInChildren<MeshRenderer>();
        foreach (MeshRenderer renderer in renderers)
        {
            renderer.gameObject.isStatic = true;
        }

        Debug.Log("✅ TDM arena setup complete");
    }

    void SetupSpawnPoints()
    {
        Debug.Log("📍 Setting up spawn points...");

        GameObject spawnParent = new GameObject("SpawnPoints");

        // Team A spawn points
        GameObject teamASpawns = new GameObject("TeamA_Spawns");
        teamASpawns.transform.SetParent(spawnParent.transform);

        Vector3[] teamAPositions = {
            new Vector3(-30, 0, 30),
            new Vector3(-25, 0, 30),
            new Vector3(-30, 0, 25),
            new Vector3(-25, 0, 25),
            new Vector3(-27.5f, 0, 27.5f)
        };

        for (int i = 0; i < teamAPositions.Length; i++)
        {
            GameObject spawn = new GameObject($"TeamA_Spawn_{i}");
            spawn.transform.SetParent(teamASpawns.transform);
            spawn.transform.position = teamAPositions[i];
        }

        // Team B spawn points
        GameObject teamBSpawns = new GameObject("TeamB_Spawns");
        teamBSpawns.transform.SetParent(spawnParent.transform);

        Vector3[] teamBPositions = {
            new Vector3(30, 0, -30),
            new Vector3(25, 0, -30),
            new Vector3(30, 0, -25),
            new Vector3(25, 0, -25),
            new Vector3(27.5f, 0, -27.5f)
        };

        for (int i = 0; i < teamBPositions.Length; i++)
        {
            GameObject spawn = new GameObject($"TeamB_Spawn_{i}");
            spawn.transform.SetParent(teamBSpawns.transform);
            spawn.transform.position = teamBPositions[i];
        }

        Debug.Log("✅ Spawn points created");
    }

    void CreateCaptureZones()
    {
        Debug.Log("🎯 Creating capture zones...");

        GameObject zoneParent = new GameObject("CaptureZones");

        Vector3[] zonePositions = {
            new Vector3(0, 0, 0),      // Center
            new Vector3(-15, 0, 15),   // North West
            new Vector3(15, 0, 15),    // North East
            new Vector3(-15, 0, -15),  // South West
            new Vector3(15, 0, -15)    // South East
        };

        for (int i = 0; i < zonePositions.Length; i++)
        {
            GameObject zone = new GameObject($"CaptureZone_{i}");
            zone.transform.SetParent(zoneParent.transform);
            zone.transform.position = zonePositions[i];

            CaptureZone captureZone = zone.AddComponent<CaptureZone>();
            captureZone.Initialize($"Zone {i + 1}", 5f, 10f);
        }

        Debug.Log("✅ Capture zones created");
    }

    void SetupWeaponSpawns()
    {
        Debug.Log("🔫 Setting up weapon spawns...");

        GameObject weaponParent = new GameObject("WeaponSpawns");

        Vector3[] weaponPositions = {
            new Vector3(0, 0, 20),
            new Vector3(20, 0, 0),
            new Vector3(0, 0, -20),
            new Vector3(-20, 0, 0),
            new Vector3(10, 0, 10),
            new Vector3(-10, 0, 10),
            new Vector3(10, 0, -10),
            new Vector3(-10, 0, -10)
        };

        for (int i = 0; i < weaponPositions.Length; i++)
        {
            GameObject spawn = new GameObject($"WeaponSpawn_{i}");
            spawn.transform.SetParent(weaponParent.transform);
            spawn.transform.position = weaponPositions[i];
        }

        Debug.Log("✅ Weapon spawns created");
    }

    void GenerateTDMTrainingConfig()
    {
        Debug.Log("⚙️ Generating TDM training config...");

        string configContent = @"behaviors:
  VictorTDM:
    trainer_type: poca
    hyperparameters:
      batch_size: 2048
      buffer_size: 20480
      learning_rate: 3e-4
      beta: 5e-3
      epsilon: 0.2
      lambd: 0.95
      num_epoch: 3
      learning_rate_schedule: linear

    network_settings:
      normalize: true
      hidden_units: 512
      num_layers: 3
      vis_encode_type: simple
      memory:
        sequence_length: 64
        memory_size: 256

    reward_signals:
      extrinsic:
        gamma: 0.99
        strength: 1.0
      curiosity:
        gamma: 0.99
        strength: 0.02
        encoding_size: 256
        learning_rate: 1e-3

    max_steps: 5000000
    time_horizon: 1000
    summary_freq: 10000
    threaded: true

    self_play:
      save_steps: 50000
      team_change: 200000
      swap_steps: 10000
      play_against_latest_model_ratio: 0.5
      window: 10

env_settings:
  env_path: null
  env_args: null
  base_port: 5005
  num_envs: 1
  seed: -1

engine_settings:
  width: 1280
  height: 720
  quality_level: 1
  time_scale: 20
  target_frame_rate: 60
  capture_frame_rate: 60
  no_graphics: false

checkpoint_settings:
  run_id: victor_tdm_training
  initialize_from: null
  load_model: false
  resume: false
  force: false
  train_model: true
  inference: false
  results_dir: results";

        Directory.CreateDirectory("config");
        File.WriteAllText("config/victor_tdm_config.yaml", configContent);

        Debug.Log("✅ TDM training config generated");
        EditorUtility.DisplayDialog("Success", "TDM training config generated at config/victor_tdm_config.yaml", "OK");
    }

    void CreateTeamMaterials()
    {
        Debug.Log("🎨 Creating team materials...");

        Directory.CreateDirectory("Assets/Materials/Teams");

        // Team A (Blue) material
        Material teamAMat = new Material(Shader.Find("Standard"));
        teamAMat.name = "TeamA_Blue";
        teamAMat.color = Color.blue;
        AssetDatabase.CreateAsset(teamAMat, "Assets/Materials/Teams/TeamA_Blue.mat");

        // Team B (Red) material
        Material teamBMat = new Material(Shader.Find("Standard"));
        teamBMat.name = "TeamB_Red";
        teamBMat.color = Color.red;
        AssetDatabase.CreateAsset(teamBMat, "Assets/Materials/Teams/TeamB_Red.mat");

        Debug.Log("✅ Team materials created");
    }

    void SetupNavMesh()
    {
        Debug.Log("🗺️ Setting up NavMesh...");

        // This would typically involve NavMesh baking
        // For now, just log the instruction
        Debug.Log("📝 Please bake NavMesh manually: Window → AI → Navigation");
        EditorUtility.DisplayDialog("NavMesh Setup",
            "Please bake NavMesh manually:\n\n1. Go to Window → AI → Navigation\n2. Select all static geometry\n3. Click 'Bake'", "OK");
    }

    void ValidateTDMSetup()
    {
        Debug.Log("🧪 Validating TDM setup...");

        List<string> issues = new List<string>();

        // Check for SquadManager
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager == null)
        {
            issues.Add("❌ No SquadManager found");
        }

        // Check for TDMEnvironment
        TDMEnvironment tdmEnv = FindObjectOfType<TDMEnvironment>();
        if (tdmEnv == null)
        {
            issues.Add("❌ No TDMEnvironment found");
        }

        // Check for capture zones
        CaptureZone[] zones = FindObjectsOfType<CaptureZone>();
        if (zones.Length == 0)
        {
            issues.Add("❌ No capture zones found");
        }

        // Check for spawn points
        GameObject teamASpawns = GameObject.Find("TeamA_Spawns");
        GameObject teamBSpawns = GameObject.Find("TeamB_Spawns");
        if (teamASpawns == null || teamBSpawns == null)
        {
            issues.Add("❌ Spawn points not found");
        }

        // Display results
        if (issues.Count == 0)
        {
            Debug.Log("✅ TDM setup validation passed!");
            EditorUtility.DisplayDialog("Validation Success", "TDM setup is complete and valid!", "OK");
        }
        else
        {
            string issueList = string.Join("\n", issues);
            Debug.LogWarning($"⚠️ TDM setup issues:\n{issueList}");
            EditorUtility.DisplayDialog("Validation Issues", $"Issues found:\n{issueList}", "OK");
        }
    }
}
